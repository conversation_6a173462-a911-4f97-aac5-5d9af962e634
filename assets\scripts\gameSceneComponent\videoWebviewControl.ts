const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  webview: cc.WebView = null;

  // @property(cc.Node)
  // maskSuccess: cc.Node = null;

  // 保存原始宽高
  private originalWidth: number = 0;
  private originalHeight: number = 0;
  onLoad() {}
  onWebviewFinishEvent(webview, eventType, customEventData) {}
  TestCode() {
    cc.log('WebView调用了TestCode');
  }
  onDestroy() {
    // this.VideoPlayer.off('ready-to-play', this.onVideoPlayerEvent, this);
  }
}

const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  VideoPlayer: cc.Node = null;

  // @property(cc.Node)
  // maskSuccess: cc.Node = null;

  // 保存原始宽高
  private originalWidth: number = 0;
  private originalHeight: number = 0;
  onLoad() {
    // 记录原始宽高
    this.originalWidth = this.VideoPlayer.width;
    this.originalHeight = this.VideoPlayer.height;
    let videoStr =
      '<video class="cocosVideo" preload="auto" webkit-playsinline="" x5-playsinline="" playsinline="" style="position: absolute; bottom: 0px; left: 0px; z-index: 0; visibility: visible; width: 500px; height: 280px; transform: matrix(0.585938, 0, 0, 0.585938, 41.0156, -323.969); transform-origin: 0px 100% 0px;"><source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.mp4"><source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.ogg"><source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.ogv"><source src="assets/resources/native/fa/fa1b8b9f-c738-429c-a0db-2e8943dda0d6.webm"></video>';
    let gameDiv = document.querySelector('#GameDiv');
    cc.log('gameDiv', gameDiv);
    // 将videoStr插入到gameDiv中
    gameDiv.insertAdjacentHTML('beforeend', videoStr);
    // this.VideoPlayer.on('ready-to-play', this.onVideoPlayerEvent, this);
    // this.scheduleOnce(() => {
    //   let videos = document.getElementsByTagName('video');
    //   if (videos.length > 0) {
    //     let v = videos[0];
    //     v.style.position = 'absolute';
    //     v.style.zIndex = '9999'; // 确保在前面（按需调整）
    //     v.style.left = '50%';
    //     v.style.top = '50%';
    //     v.style.transform = 'translate(-50%, -50%) !important';
    //     v.style.transformOrigin = 'center center !important';
    //     v.style.width = '100vw'; // 占满屏幕宽
    //     v.style.height = 'auto'; // 按比例自适应高
    //     v.style.maxHeight = '100vh'; // 防止高度超出屏幕3
    //   }
    // }, 1);
    // console.log('Position:', this.VideoPlayer.position);
    // console.log('AnchorPoint:', this.VideoPlayer.getAnchorPoint());
    // cc.log('Content size:', this.VideoPlayer.getContentSize());

    let videos = document.getElementsByTagName('video');
    if (videos.length > 0) {
      let v = videos[0];
      // 假设你能拿到 video 元素
      let video = document.querySelector('video');

      // 去掉 Cocos 自动加的矩阵
      video.style.transform = 'none';

      // 自己设定横屏旋转 & 居中
      video.style.transform = 'rotate(-90deg) translate(-50%, -50%)';
      // 先 rotate 再 translate 才能保持中心点正确
      video.style.transformOrigin = 'center center';

      // 让它保持原本大小
      video.style.width = '500px';
      video.style.height = '280px';

      // 居中
      video.style.position = 'absolute';
      video.style.top = '50%';
      video.style.left = '50%';
      // this.rotateVideo90deg(v);
    }
  }
  onVideoPlayerEvent(player, eventType, customEventData) {
    cc.log('videoplayer', player);
    cc.log('eventType', eventType);

    if (eventType === cc.VideoPlayer.EventType.READY_TO_PLAY) {
      // 若视频准备好了，这个事件并不保障会在所有平台或浏览器中被触发，它依赖于平台实现，请不要依赖于这个事件做视频播放的控制。
      cc.log('视频准备播放了');

      // this.scheduleOnce(() => {
      //   player.play();
      // }, 500);
      // setTimeout(() => {
      //   let videos = document.getElementsByTagName('video');
      //   if (videos.length > 0) {
      //     let v = videos[0];
      //     // 假设你能拿到 video 元素
      //     let video = document.querySelector('video');

      //     // 让它保持原本大小
      //     video.style.width = '500px';
      //     video.style.height = '280px';
      //     // 居中
      //     video.style.position = 'absolute';
      //     video.style.top = '50%';
      //     video.style.left = '50%';
      //     video.style.bottom = 'initial';

      //     // 先 rotate 再 translate 才能保持中心点正确
      //     video.style.transformOrigin = 'center center';
      //     // 去掉 Cocos 自动加的矩阵
      //     video.style.transform = 'none';
      //     // 自己设定横屏旋转 & 居中
      //     video.style.transform = 'rotate(-90deg) translate(-50%, -50%)';

      //     // this.rotateVideo90deg(v);
      //   }
      // }, 5000);
    } else if (eventType === cc.VideoPlayer.EventType.PLAYING) {
      // 若视频正在播放,do something...
      cc.log('视频正在播放');
    } else if (eventType === cc.VideoPlayer.EventType.PAUSED) {
      // 若视频暂停, do something...
      cc.log('视频暂停了');
    } else if (eventType === cc.VideoPlayer.EventType.STOPPED) {
      // 若视频停止, do something...
      cc.log('视频停止了');
    } else if (eventType === cc.VideoPlayer.EventType.COMPLETED) {
      // 若播放结束,do something...
      cc.log('视频的元信息已加载完成');
    } else if (eventType === cc.VideoPlayer.EventType.META_LOADED) {
      // 若视频的元信息已加载完成，你可以调用 getDuration 来获取视频总时长1
      cc.log('视频元信息已加载完成');
    } else if (eventType === cc.VideoPlayer.EventType.CLICKED) {
      // 若点击了视频, do something...
      // 勾选了StayOnBottom后这个不能用
      cc.log('点击了视频');
    }
    //这里 videoplayer 是一个 VideoPlayer 组件对象实例
    // 这里的 eventType === cc.VideoPlayer.EventType enum 里面的值
    //这里的 customEventData 参数就等于你之前设置的 "foobar"
  }
  rotateVideo90deg(video) {
    // let v = video;
    // v.style.position = 'absolute';
    // v.style.zIndex = '9999'; // 确保在前面（按需调整）
    // v.style.left = '50%';
    // v.style.top = '50%';
    // v.style.transform = 'translate(-50%, -50%) !important';
    // v.style.transformOrigin = 'center center !important';
    // v.style.width = '100vw'; // 占满屏幕宽
    // v.style.height = 'auto'; // 按比例自适应高
    // v.style.maxHeight = '100vh'; // 防止高度超出屏幕3
    // 1. 获取当前 transform 矩阵
    const style = window.getComputedStyle(video);
    const matrix = new DOMMatrixReadOnly(style.transform);
    cc.log('matrix', matrix);
    // 2. 元素的大小（包含缩放）
    const rect = video.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    cc.log('rect', rect);
    // 3. 转换：平移到中心 → 旋转 90° → 平移回去
    const rotated = new DOMMatrix()
      .translate(centerX, centerY)
      .rotate(90) // 旋转90度
      .translate(-centerX, -centerY)
      .multiply(matrix);
    // 4. 应用新的矩阵
    video.style.transform = `matrix(${rotated.a}, ${rotated.b}, ${rotated.c}, ${rotated.d}, ${rotated.e}, ${rotated.f})`;
  }
  onDestroy() {
    // this.VideoPlayer.off('ready-to-play', this.onVideoPlayerEvent, this);
  }
}
